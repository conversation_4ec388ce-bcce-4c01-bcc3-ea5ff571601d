# 多阶段银行客服合规审计系统文档

## 系统概述

本系统实现了一个四阶段的银行客服问答对合规审计流程，以结构化的多步骤方法取代原有的单一模型调用方式。系统保持了现有的输入/输出逻辑和文件保存机制，同时提供了更精细化和专业化的合规评估能力。

## 系统架构

### 核心文件结构
```
├── multi_stage_config.py              # 多阶段配置文件
├── multi_stage_compliance_evaluator.py # 主要编排脚本
├── test_multi_stage_compliance.py     # 测试脚本
├── 多阶段合规审查结果/                 # 输出目录
│   ├── 中间结果/                      # 各阶段中间结果
│   │   ├── 第一阶段_合理性检查/
│   │   ├── 第二阶段_问题分类/
│   │   ├── 第三阶段_类型评估/
│   │   └── 第四阶段_最终决策/
│   └── 最终合规结果/                  # 最终结果（兼容原格式）
```

## 四阶段流程详解

### 第一阶段：合理性检查
**目标：** 评估问题是否在银行业务背景下合理

**评估标准：**
- 问题是否与银行业务相关
- 问题是否是客户在实际业务场景中可能遇到的
- 问题表述是否清晰、具体
- 问题是否具有实际业务意义

**输出格式：**
```json
{
    "reasonableness_judgment": "合理/不合理",
    "confidence_score": 0.95,
    "reasoning": "判断理由",
    "business_relevance": "高/中/低",
    "stage1_result": "通过/不通过"
}
```

**决策逻辑：**
- 如果问题不合理，直接判定为不合规，终止后续流程
- 只有合理的问题才能进入第二阶段

### 第二阶段：问题类型分类
**目标：** 将问题归类为五种特定类型之一

**问题类型定义：**

1. **定义/概念解释问题 (DEFINITION)**
   - 关键词：什么是、是什么、定义、概念、含义
   - 示例：什么是定期存款？

2. **操作问题 (OPERATION)**
   - 关键词：怎么办、如何做、怎样做、操作、流程
   - 示例：如何进行跨行转账？

3. **审核失败/文件补充问题 (AUDIT_FAILURE)**
   - 关键词：审核失败、补充、资料、材料、文件、被拒
   - 示例：审核失败需要补充什么材料？

4. **是/否问题 (YES_NO)**
   - 关键词：是否、能否、可以、允许、可不可以
   - 示例：理财产品是否可以提前赎回？

5. **事实性/趋势性问题 (FACTUAL)**
   - 关键词：什么时候、哪种、更、比较、趋势、概率
   - 示例：什么时候利率更低？

**输出格式：**
```json
{
    "question_type": "DEFINITION/OPERATION/AUDIT_FAILURE/YES_NO/FACTUAL",
    "type_name": "具体类型名称",
    "confidence_score": 0.95,
    "classification_reasoning": "分类理由",
    "keywords_matched": ["匹配的关键词"],
    "stage2_result": "分类完成"
}
```

### 第三阶段：特定类型合规评估
**目标：** 针对不同问题类型使用专门的评估标准

#### 定义类问题评估重点：
- 回答是否准确解释了概念
- 解释是否清晰易懂，无歧义
- 无需要求提供操作细节

#### 操作类问题评估重点：
- 是否提供了可执行的操作步骤
- 步骤是否清晰、有序
- 是否包含必要的注意事项

#### 审核失败类问题评估重点：
- 是否提供了合理的排查建议
- 是否提供了官方联系方式
- 对于无法知悉具体原因的情况处理是否得当

#### 是否类问题评估重点：
- 是否给出了明确的是/否答复
- 简洁确认式回复是否合适
- 回答是否准确无误

#### 事实性问题评估重点：
- 陈述是否有充分的数据支撑
- 是否避免了无依据的强断言
- 是否标注了数据来源

### 第四阶段：最终决策综合
**目标：** 综合前三阶段结果，生成最终合规判定

**输入信息：**
- 第一阶段：合理性检查结果
- 第二阶段：问题类型分类结果
- 第三阶段：特定类型合规评估结果

**输出要求：**
- 必须严格按照原始系统的5轮分析格式输出
- 确保与现有系统完全兼容
- 提供综合的优化建议

## 配置说明

### 多阶段流程配置
```python
MULTI_STAGE_CONFIG = {
    "enable_multi_stage": True,           # 启用多阶段模式
    "save_intermediate_results": True,    # 保存中间结果
    "stage_delay": 1,                     # 阶段间延迟（秒）
    "max_retries": 3,                     # 最大重试次数
    "output_folder": "多阶段合规审查结果",
    "intermediate_folder": "中间结果"
}
```

### API配置
继承原有的API配置，包括：
- API密钥和基础URL
- 模型参数（温度、最大token数等）
- 请求延迟设置

## 使用方法

### 基本使用
```python
from multi_stage_compliance_evaluator import MultiStageComplianceEvaluator

# 创建评估器
evaluator = MultiStageComplianceEvaluator()

# 处理单个问答对
qa_data = {...}  # 问答对数据
result = evaluator.process_single_qa(qa_data, "file_id")

# 批量处理文件夹
results = evaluator.process_qa_folder("问答对100-201")
```

### 测试系统
```bash
python test_multi_stage_compliance.py
```

### 运行完整评估
```bash
python multi_stage_compliance_evaluator.py
```

## 输出格式

### 最终结果格式
系统输出与原有系统完全兼容的结果格式：
```json
{
    "文件名": "001.json",
    "原始数据": {...},
    "评估时间": "2025-08-02 10:30:00",
    "合规评估结果": "完整的5轮分析格式文本",
    "评估状态": "成功"
}
```

### 多阶段详细结果
额外提供多阶段详细信息：
```json
{
    "文件ID": "001",
    "多阶段结果": {
        "stage1": {...},  # 合理性检查结果
        "stage2": {...},  # 问题分类结果
        "stage3": {...},  # 类型评估结果
        "stage4": {...}   # 最终决策结果
    },
    "最终合规判断": "合规/不合规",
    "判断理由": "...",
    "评估状态": "成功"
}
```

## 与原系统的对比

### 原系统特点
- 单一模型调用
- 一次性完成所有分析
- 5轮分析在一个提示词中完成

### 新系统优势
1. **结构化流程**：四个明确的阶段，每个阶段专注特定任务
2. **专业化评估**：针对不同问题类型使用专门的评估标准
3. **可追溯性**：每个阶段的结果都被保存，便于调试和优化
4. **模块化设计**：每个阶段可以独立测试和优化
5. **错误处理**：更好的错误处理和重试机制
6. **兼容性**：完全兼容原有的输入/输出格式

### 保持的特性
- 相同的输入数据格式
- 相同的最终输出格式
- 相同的文件保存机制
- 相同的5轮分析框架（在第四阶段中实现）

## 错误处理

### 阶段失败处理
- 每个阶段都有重试机制
- API调用失败时自动重试
- JSON解析失败时提供多种解析策略

### 流程终止条件
- 第一阶段判定问题不合理时，直接终止流程
- 任何阶段出现严重错误时，记录错误信息并继续处理下一个文件

## 性能考虑

### 优化措施
- 阶段间适当延迟，避免API限制
- 批量处理时的进度跟踪
- 中间结果缓存，避免重复计算

### 资源使用
- 相比原系统，API调用次数增加4倍
- 但每次调用的复杂度降低
- 总体处理时间可能略有增加，但准确性显著提升

## 扩展性

### 新增问题类型
在`multi_stage_config.py`中的`QUESTION_TYPES`添加新类型，并在`STAGE3_TYPE_SPECIFIC_PROMPTS`中添加对应的评估提示词。

### 新增评估维度
可以在第三阶段的类型特定评估中添加新的评估维度和评分标准。

### 自定义输出格式
可以通过修改第四阶段的提示词来调整最终输出格式。
