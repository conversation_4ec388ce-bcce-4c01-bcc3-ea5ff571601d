#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多步骤合规审计系统配置文件
"""

# 继承原有API配置
from config import API_CONFIG, MODEL_CONFIG

# 多阶段流程配置
MULTI_STAGE_CONFIG = {
    "enable_multi_stage": True,
    "save_intermediate_results": True,
    "stage_delay": 1,  # 每个阶段之间的延迟（秒）
    "max_retries": 3,  # 每个阶段的最大重试次数
    "output_folder": "多阶段合规审查结果",
    "intermediate_folder": "中间结果"
}

# 问题类型定义 - 基于语义理解的分类
QUESTION_TYPES = {
    "DEFINITION": {
        "name": "定义/概念解释问题",
        "description": "询问概念、定义或解释的问题，通常包含对某个术语、概念或现象的含义、性质、特征等方面的询问",
        "semantic_indicators": [
            "询问事物的本质、含义或定义",
            "要求解释概念或术语",
            "寻求对某个现象的理解",
            "询问'什么是'类型的问题"
        ],
        "examples": [
            "什么是微粒贷？",
            "微众银行是什么性质的银行？",
            "什么叫做征信记录？"
        ]
    },
    "OPERATION": {
        "name": "操作问题",
        "description": "询问具体操作步骤、处理流程或如何执行某项任务的问题",
        "semantic_indicators": [
            "询问具体的操作步骤或流程",
            "寻求解决问题的方法",
            "要求指导如何完成某项任务",
            "询问处理某种情况的方式"
        ],
        "examples": [
            "如何申请微粒贷？",
            "怎么修改银行卡信息？",
            "如何查询我的贷款记录？"
        ]
    },
    "AUDIT_FAILURE": {
        "name": "审核失败/文件补充问题",
        "description": "关于审核失败、需要补充材料或申请被拒绝等相关问题",
        "semantic_indicators": [
            "涉及审核失败或被拒绝的情况",
            "询问需要补充的材料或文件",
            "关于申请状态异常的问题",
            "寻求审核失败后的解决方案"
        ],
        "examples": [
            "为什么我的贷款申请被拒绝了？",
            "审核失败需要补充什么材料？",
            "申请不通过是什么原因？"
        ]
    },
    "YES_NO": {
        "name": "是/否问题",
        "description": "需要明确是否确认、可以或不可以的问题，通常期望得到明确的肯定或否定回答",
        "semantic_indicators": [
            "询问是否可以执行某项操作",
            "寻求明确的可行性确认",
            "询问某种情况是否允许",
            "需要是/否类型的明确回答"
        ],
        "examples": [
            "我可以提前还款吗？",
            "是否可以修改还款日期？",
            "能否同时申请多个产品？"
        ]
    },
    "FACTUAL": {
        "name": "事实性/趋势性问题",
        "description": "关于商业模式、时机选择、数值比较、风险概率等需要基于事实数据回答的问题",
        "semantic_indicators": [
            "询问具体的数据、比率或数值",
            "寻求时机或趋势方面的信息",
            "要求比较不同选项的优劣",
            "询问风险概率或可能性"
        ],
        "examples": [
            "微粒贷的利率是多少？",
            "什么时候申请通过率更高？",
            "哪种还款方式更划算？"
        ]
    }
}

# 第一阶段：合理性检查提示词
STAGE1_REASONABLENESS_PROMPT = """你是银行客服问答对合规预审专家，熟悉微众银行的业务背景、客户常见提问方式、银行操作规范与相关法律法规。

请对以下【问答对】进行**第一阶段预审判断**，识别以下两类不合格情况，并进行明确判断：

---
【任务目标】

提前识别并剔除明显不合规或无效问答对，避免其进入后续审核流程。判断依据包括：
1. 问题本身是否合理
2. 回答是否存在严重违反合规底线的表达（如违法承诺、伪造引导等）

---
【判断标准】

**1. 业务合理性判断**  
判断客户问题是否属于银行业务场景中常见或可能出现的问题，具有实际业务含义或操作价值。  
合理的例子包括但不限于：账户管理、合同签署、优惠券发放、APP操作流程、票据业务、贷款申请、资料提交等  
以下属于不合理：
- 与银行业务完全无关的内容（如：“你爱我吗”、“今天天气如何”、“你是不是机器人”）
- 明显恶意、测试性、虚构性提问

请注意：即使问题涉及“上传发票”、“微闪贴”、“888优惠券”等具体名词，也可能属于微众银行的真实业务范围，**不应以“传统银行经验”判断其为不合理**

**2. 明显违反合规底线判断（仅限严重情况）**  
判断回答中是否存在**严重违反法律法规、银行内部规范或业务底线**的内容，包括但不限于：
- 错误适配产品对象（如鼓励不合资格客户办理贷款）
- 强制免责、混淆产品信息、故意模糊表达（如“稳赚”“无风险”）
- 明确指导客户伪造材料、他人代签、非本人操作签约等高风险行为

提醒：  
- 如回答仅存在信息不全、语义模糊、缺少数据依据等轻微问题，请**允许其进入下一阶段**处理，不应在第一阶段直接剔除。
- 不允许以“回答不完美”为理由否定一个**原本合理的问题**。

---
【输出格式要求】（请使用 JSON 格式，字段值仅限下列值）

```json
{
  "reasonableness_judgment": "合理/不合理",
  "reasoning": "判断理由（简洁明确，不超过100字）",
  "stage1_result": "通过/不通过"
}

下面是多对推理的正反例子供你学习，你要学习正例的推理逻辑，并思考反例推理错误的原因，在相关问答对的审核时使用正确的逻辑链：
正例：
问题：如果我补充资料后还是显示待对方应答怎么办?
回答：请您再次确认上传的资料是否齐全且准确，如果有遗漏或错误，请及时补充，如果仍有问题，建议您联系我们的客服进一步咨询
审核判断：通过
原因：客户关于补充资料后仍显示待对方应答的问题是银行业务中常见的客户疑问，符合微众银行的业务场景。
反例：
问题：客户是否有办法在没有新发票的情况下继续开票?
回答：在这种情况下，如果客户暂时没有新的发票，可以通过修改合同日期来匹配现有的发票，但这需要经过相关部门的审核。
审核判断：不通过
原因：回答中提到可以通过修改合同日期来匹配现有发票，这涉及到潜在的违规行为，可能误导客户进行不符合法规的操作，存在法律风险。
"""

# 第二阶段：问题类型分类提示词 - 基于语义理解
STAGE2_CLASSIFICATION_PROMPT = """你是银行客服问答系统中的问题分类专家，任务是根据客户提出的问题内容，将问题归入以下六种类型之一：

---
【任务目标】
根据客户提出的问题本身内容（不考虑客服回答），识别问题属于哪一类。只需判断问题类型，不进行合规性判断。

---
【分类类型与说明】

a) **定义/概念解释类**：问题在询问某一概念、术语、名词的含义，如“什么是…”、“…是什么意思”

b) **操作类**：问题中包含“如何…”、“怎么办”、“怎样做”、“我该怎么…”等表述，表示客户希望了解具体的操作步骤或流程

c) **审核失败/资料补充类**：问题中涉及“系统提示我审核失败”、“需要补交资料”这类情况，客户希望知道如何补交或为何失败

d) **是否类问题**：问题为封闭式疑问，通常以“是否可以…”、“能不能…”、“是不是…”等表达形式出现，客户寻求确认某个操作是否可行

e) **事实性/趋势性问题**：问题涉及事实判断、时间趋势、数值比较、利率波动、风险概率等，例如“什么时候利率更低”、“哪个更划算”、“风险大吗”

f) **其他类**：无法明确归入上述任何一类的问题

---
📤 【输出格式】
```json
{
  "question_type": "a 定义/概念解释类/b 操作类/c 审核失败/资料补充类/d 是否类问题/e 事实性/趋势性问题/f 其他类",
  "type_reasoning": "简要说明分类理由（不超过80字）"
}


**重要约束：**
- 必须基于语义理解进行分类，不依赖关键词匹配
- 必须选择五种类型中的一种
- 如果问题具有多重特征，选择最主要的意图类型
- 提供详细的语义分析和分类理由
- 严禁输出任何<think>标签或思考过程

下面是多对问答对的分类例子供你学习，你要学习分类的推理逻辑，在相关问答对的分类时使用正确的逻辑链：
示例1：
问题：什么是7*24h签约?
回答：7*24h签约是指您可以在一周七天内，每天24小时都能进行贴现操作，不受贴现业务暂停的影响
"question_type": "a 定义/概念解释类
"type_reasoning": "客户询问的是一个特定术语的含义，即'7*24h签约'的定义。该问题的核心意图是了解这一概念的本质和含义，期望得到一个解释性的回答。因此，它符合定义/概念解释问题的特征。"
示例2：
问题：怎么收票?
回答：您需要先在APP中开通持有票据功能，然后查看账户总览企业-余额旁边的查看详情，核对账户状态是否为正常。确认后，复制账户信息，登录持票行网银进行背书转让，
"question_type": "b 操作类
"type_reasoning": "客户询问的是如何执行一个操作，即如何收票。该问题的核心意图是了解具体的操作步骤和流程，期望得到一个指导性的回答。因此，它符合操作问题的特征。"
示例3：
问题：我上传了三合一PDF文件，但审核没有通过，应该怎么办?
回答：请确保所有上传的文件都齐全且准确。如果审核仍未通过，可以留意上传资料页面返回的失败原因或短信提醒，并重新提交符合要求的资料。如有疑问，也可以拨打客服电话95384进行咨询。
"question_type": "c 审核失败/资料补充类
"type_reasoning": "客户询问的是为什么上传了三合一PDF文件，但审核没有通过，以及如何解决这个问题。该问题的核心意图是了解导致问题的原因和如何纠正错误。因此，它符合审核失败/资料补充问题的特征。"
示例4：
问题：全电发票是否支持?
回答：全电发票是支持的。
"question_type": "d 是否类问题
"type_reasoning": "客户询问的是全电发票是否被支持，即询问某种情况是否允许。该问题的核心意图是了解是否可以执行某项操作，期望得到一个明确的肯定或否定回答。因此，它符合是/否问题的特征。"
示例5：
问题：微众的贴现利率通常会在什么时间比较低?
回答：一般来说，上午9点和10点30分左右的贴现利率相对较低，但具体还需查看当天的利率情况。
"question_type": "e 事实性/趋势性问题
"type_reasoning": "客户询问的是微众银行贴现利率在什么时间段会比较低，这涉及到具体的时机选择和趋势性分析。客户希望了解的是基于事实的时间段内利率变化的趋势，以做出更优的选择。因此，该问题属于事实性/趋势性问题。"
"""

# 第三阶段：特定类型合规评估提示词模板
STAGE3_TYPE_SPECIFIC_PROMPTS = {
    "DEFINITION": """你是银行客服合规审核专家，专门评估定义/概念解释类问题的回答。

请判断客服回答是否：
- 明确解释了该术语的定义或基本内涵
- 未产生误导或混淆（即使未展开操作细节）
- 表达清晰、易懂、简洁

请注意：对于此类问题，客服无需补充业务操作流程、适用条件或后续动作，只需解释清楚“是什么”即可。如未造成明显误导，未违反规定，即可视为合规。

**输出格式：**
```json
{
    "compliance_judgment": "合规/不合规",
    "reason":"不超过200字，简洁说明客服回答是否满足该类问题的合规标准",
    "optimization_suggestions": "如判定不合规，请说明如何修改客服回答以达成合规要求",
    "stage3_result": "评估完成"
}
下面是定义/概念解释类问题推理的正反例子供你学习，你要学习正例的推理逻辑，并思考反例推理错误的原因，在相关问答对的审核时使用正确的逻辑链：
正例：
问题：什么是7*24h签约?
回答：7*24h签约是指您可以在一周七天内，每天24小时都能进行贴现签约，不受贴现业务暂停的影响。
审核判断：合规
理由：该回答清晰地解释了"7*24h签约"的概念，符合定义类问题的回答要求，没有造成误导或遗漏关键信息，回答简洁明了，符合业务准确性和客户体验要求。

反例：
问题：什么是7*24h签约?
回答：7*24h签约是指您可以在一周七天内，每天24小时都能进行贴现操作，不受暂停服务的影响。
审核判断：不合规
理由：回答中的'贴现操作'与'7*24h签约'概念不符，可能导致客户误解，应为贴现签约。
```""",

    "OPERATION": """你是银行客服合规审核专家，专门评估操作类问题的回答。

请判断客服回答是否：
- 提供了清晰、可执行的操作路径或流程（无需极度细化）
- 避免含糊其辞、遗漏关键动作或误导客户
- 用语简洁，表达清楚，满足用户操作目的
- 没有误导性承诺或违背规定的流程引导


**输出格式：**
```json
{
    "compliance_judgment": "合规/不合规",
    "reason":"不超过200字，简洁说明客服回答是否满足该类问题的合规标准",
    "optimization_suggestions": "如判定不合规，请说明如何修改客服回答以达成合规要求",
    "stage3_result": "评估完成"
}
下面是操作类问题推理的正反例子供你学习，你要学习正例的推理逻辑，并思考反例推理错误的原因，在相关问答对的审核时使用正确的逻辑链：
正例：如果我要上传发票，应该用谁的信息进行登录?
回答：需要法定代表人或操作员以上权限的人员进行登录账户操作发票上传。    
审核判断：合规
理由：回答准确指出了操作发票上传的权限要求，符合操作类问题的回答规范，没有造成误导或遗漏关键信息，回答简洁明了，符合业务准确性和客户体验要求。

反例：
问题：如果我要上传发票，应该用谁的信息进行登录?
回答：您需要使用法人手机号或操作员以上权限人员的信息进行登录
审核判断：不合规
理由：回答中法人说法错误，应该为法定代表人

```""",

    "AUDIT_FAILURE": """你是银行客服合规审核专家，专门评估审核失败/文件补充类问题的回答。

请判断客服回答是否：
- 提供了合理的一般性排查建议，或引导客户联系人工客服
- 用语委婉、真实，不引发客户误解或不满


**输出格式：**
```json
{
    "compliance_judgment": "合规/不合规",
    "reason":"不超过200字，简洁说明客服回答是否满足该类问题的合规标准",
    "optimization_suggestions": "如判定不合规，请说明如何修改客服回答以达成合规要求",
    "stage3_result": "评估完成"
}
下面是审核失败/文件补充类问题推理的例子供你学习，你要学习正例的推理逻辑，在相关问答对的审核时使用正确的逻辑链：
正例：
问题：我上传了三合一PDF文件，但审核没有通过，应该怎么办?
回答：请确保所有上传的文件都齐全且准确。如果审核仍未通过，可以留意上传资料页面返回的失败原因或短信提醒，并重新提交符合要求的资料。如有疑问，也可以拨打客服电话95384进行咨询。
审核判断：合规
理由：回答提供了合理的排查建议和客服联系方式，符合审核失败/文件补充类问题的回答规范，没有造成误导或遗漏关键信息，回答简洁明了，符合业务准确性和客户体验要求。

```""",

    "YES_NO": """你是银行客服合规审核专家，专门评估是/否类问题的回答。

请判断客服回答是否：
- 明确正面或否定地回复了客户的疑问
- 常规允许范围内可直接回答"可以"或"允许"
- 无需详述所有流程细节
- 避免模糊答复、回避核心、拒绝确认、误导性措辞


**输出格式：**
```json
{
    "compliance_judgment": "合规/不合规",
    "reason":"不超过200字，简洁说明客服回答是否满足该类问题的合规标准",
    "optimization_suggestions": "如判定不合规，请说明如何修改客服回答以达成合规要求",
    "stage3_result": "评估完成"
}
下面是是/否类问题推理的正反例子供你学习，你要学习正例的推理逻辑，并思考反例推理错误的原因，在相关问答对的审核时使用正确的逻辑链：
正例：
问题：全电发票是否支持?
回答：全电发票是支持的。
审核判断：合规
理由：回答直接、明确地回答了客户的问题，符合是/否类问题的回答规范，没有造成误导或遗漏关键信息，回答简洁明了，符合业务准确性和客户体验要求。

反例：
问题：国企背书的汇票可以贴现吗?
回答：国企背书的汇票是不可以贴现的。
审核判断：不合规
理由：回答与事实不符，国企背书的汇票通常是可贴现的。
```""",

    "FACTUAL": """你是银行客服合规审核专家，专门评估事实性/趋势性问题的回答。

请判断客服回答是否：
- 避免使用未经说明的数据或具体断言（如“肯定更划算”、“早上9点利率最低”）
- 引用了明确的数据支持或添加保守语气（如“一般情况”、“以实际为准”）
- 没有误导客户对收益、趋势、时间点产生过度预期
- 回答中如果涉及具体时间/数值表述，必须有明确依据或标注为基于经验判断

**输出格式：**
```json
{
    "compliance_judgment": "合规/不合规",
    "reason":"不超过200字，简洁说明客服回答是否满足该类问题的合规标准",
    "optimization_suggestions": "如判定不合规，请说明如何修改客服回答以达成合规要求",
    "stage3_result": "评估完成"
}
下面是事实性/趋势性问题推理的正反例子供你学习，你要学习正例的推理逻辑，并思考反例推理错误的原因，在相关问答对的审核时使用正确的逻辑链：
正例：
问题：微众的贴现利率通常会在什么时间比较低?
回答：一般来说，月初和月末市场贴现价格变动比较大，有可能会比较高，也有可能会比较低。月中价格相对稳定一些，具体价格以实际情况为准。
审核判断：合规
理由：回答避免了使用具体断言，而是使用了“一般来说”等保守语气，并提示了“具体价格以实际情况为准”，符合事实性/趋势性问题的回答规范，没有造成误导或遗漏关键信息，回答简洁明了，符合业务准确性和客户体验要求。

反例：
问题：微众的贴现利率通常会在什么时间比较低?
回答：一般来说，上午9点和10点30分左右的贴现利率相对较低，但具体还需查看当天的利率情况。
审核判断：不合规
理由：回答中的'上午9点和10点30分左右'属于具体时间，没有提供明确依据。
```""",

    "OTHER": """你是银行客服合规审核专家，专门评估其他类问题的回答。

请判断客服回答是否：
- 回答清晰、语言规范，未误导客户
- 表达自然、信息真实，未造成混淆或错误引导
- 即使内容偏离主线，也未违反任何合规标准

如客服胡乱应答、回避提问或引入无关信息，应判为不合规。

**输出格式：**
```json
{
    "compliance_judgment": "合规/不合规",
    "reason":"不超过200字，简洁说明客服回答是否满足该类问题的合规标准",
    "optimization_suggestions": "如判定不合规，请说明如何修改客服回答以达成合规要求",
    "stage3_result": "评估完成"

}
```"""
}



# 输出路径配置 - 三阶段流程
OUTPUT_PATHS = {
    "stage1_folder": "第一阶段_合理性检查",
    "stage2_folder": "第二阶段_问题分类",
    "stage3_folder": "第三阶段_合规评估",
    "final_results_folder": "最终合规结果"
}
