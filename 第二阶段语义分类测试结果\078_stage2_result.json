{"文件名": "078.json", "文件ID": "078", "原始数据": {"类别": "发票", "问题": "发票的开票日期有什么限制?", "回答": "发票的开票日期最早不得超过承兑汇票开票日前6个月，并且不能早于合同签署日期。", "分类": "承兑开票", "优化后的回答": "发票最早开票日不得超过承兑开票日前12个月，且发票开票日期不得早于合同签署日期。", "是否可用": "可用", "原因": null}, "第二阶段结果": {"question_type": "FACTUAL", "type_name": "事实性/趋势性问题", "classification_reasoning": "客户询问的是发票开票日期的具体限制条件，这种问题本质上是在寻求具体的规则和时间上的限定，属于事实性问题。客户的意图在于了解具体的日期范围，以确保合规操作，因此期望的回答是提供明确的时间限制和规定。", "stage2_result": "分类完成", "raw_response": "```json\n{\n    \"question_type\": \"FACTUAL\",\n    \"type_name\": \"事实性/趋势性问题\",\n    \"classification_reasoning\": \"客户询问的是发票开票日期的具体限制条件，这种问题本质上是在寻求具体的规则和时间上的限定，属于事实性问题。客户的意图在于了解具体的日期范围，以确保合规操作，因此期望的回答是提供明确的时间限制和规定。\",\n    \"stage2_result\": \"分类完成\"\n}\n```", "stage": "stage2_classification"}, "测试时间": "2025-08-03 19:57:24", "测试状态": "成功", "分类方法": "语义理解"}