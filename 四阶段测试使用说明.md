# 多步骤合规审计系统 - 四阶段测试使用说明

## 🎯 测试脚本概览

我已经为您创建了完整的四阶段测试脚本，每个阶段都有专门的测试脚本来验证功能并保存结果。

### 📁 测试脚本文件

1. **`test_stage1_complete.py`** - 第一阶段：合理性检查测试
2. **`test_stage2_complete.py`** - 第二阶段：问题类型分类测试  
3. **`test_stage3_complete.py`** - 第三阶段：特定类型合规评估测试
4. **`test_stage4_complete.py`** - 第四阶段：最终决策综合测试
5. **`test_all_stages_complete.py`** - 完整四阶段连续测试

## 🚀 使用方法

### 前提条件
```bash
# 1. 激活虚拟环境
conda activate tradingagents

# 2. 确保问答对100-201文件夹存在且包含JSON文件
```

### 方法一：逐个阶段测试（推荐）

#### 第一阶段测试
```bash
python test_stage1_complete.py
```
**输出格式示例：**
```json
{
    "reasonableness_judgment": "合理/不合理",
    "confidence_score": 0.95,
    "reasoning": "判断理由（简洁明确，不超过100字）",
    "business_relevance": "高/中/低",
    "stage1_result": "通过/不通过"
}
```

#### 第二阶段测试
```bash
python test_stage2_complete.py
```
**输出格式示例：**
```json
{
    "question_type": "DEFINITION/OPERATION/AUDIT_FAILURE/YES_NO/FACTUAL",
    "type_name": "具体类型名称",
    "confidence_score": 0.95,
    "classification_reasoning": "分类理由",
    "keywords_matched": ["匹配的关键词"],
    "stage2_result": "分类完成"
}
```

#### 第三阶段测试
```bash
python test_stage3_complete.py
```
**输出格式示例：**
```json
{
    "compliance_judgment": "合规/不合规",
    "accuracy_score": 0.95,
    "clarity_score": 0.90,
    "completeness_score": 0.85,
    "specific_issues": ["具体问题列表"],
    "optimization_suggestions": ["优化建议"],
    "stage3_result": "评估完成"
}
```

#### 第四阶段测试
```bash
python test_stage4_complete.py
```
**输出格式示例：**
```json
{
    "final_compliance_judgment": "合规/不合规",
    "final_reasoning": "最终判断理由",
    "detailed_analysis": "完整的5轮分析格式文本",
    "vote_statistics": {
        "compliant_votes": 0,
        "non_compliant_votes": 5
    },
    "final_optimization_suggestions": "综合优化建议",
    "stage4_result": "最终决策完成"
}
```

### 方法二：完整四阶段连续测试
```bash
python test_all_stages_complete.py
```

## 📂 输出目录结构

测试完成后会生成以下目录结构：

```
├── 第一阶段测试结果/
│   ├── 001_stage1_result.json
│   ├── 002_stage1_result.json
│   ├── ...
│   ├── stage1_summary_20250802_143000.json
│   └── stage1_report_20250802_143000.txt
├── 第二阶段测试结果/
│   ├── 001_stage2_result.json
│   ├── 002_stage2_result.json
│   ├── ...
│   ├── stage2_summary_20250802_144000.json
│   └── stage2_report_20250802_144000.txt
├── 第三阶段测试结果/
│   ├── 001_stage3_result.json
│   ├── 002_stage3_result.json
│   ├── ...
│   ├── stage3_summary_20250802_145000.json
│   └── stage3_report_20250802_145000.txt
├── 第四阶段测试结果/
│   ├── 001_stage4_result.json
│   ├── 002_stage4_result.json
│   ├── ...
│   ├── 兼容格式结果/
│   │   ├── 001_compatible_result.json
│   │   └── ...
│   ├── stage4_summary_20250802_150000.json
│   └── stage4_report_20250802_150000.txt
└── 综合测试报告/
    ├── comprehensive_report_20250802_150000.json
    └── comprehensive_report_20250802_150000.txt
```

## 📊 测试结果说明

### 单个文件结果格式
每个阶段都会为每个输入文件生成详细的结果文件：

```json
{
    "文件名": "001.json",
    "文件ID": "001",
    "原始数据": {...},
    "第X阶段结果": {
        // 该阶段的具体输出字段
    },
    "测试时间": "2025-08-02 14:30:00",
    "测试状态": "成功"
}
```

### 汇总报告格式
每个阶段都会生成汇总报告，包含：
- 总体统计信息
- 成功率分析
- 结果分布统计
- 详细结果列表

## 🔍 测试验证要点

### 第一阶段验证
- ✅ 合理性判断准确性
- ✅ 置信度评分合理性
- ✅ 业务相关性评估
- ✅ 不合理问题的正确识别

### 第二阶段验证
- ✅ 问题类型分类准确性
- ✅ 关键词匹配正确性
- ✅ 置信度评分合理性
- ✅ 5种问题类型的覆盖

### 第三阶段验证
- ✅ 针对不同类型的专门评估
- ✅ 类型特定评分的合理性
- ✅ 具体问题识别准确性
- ✅ 优化建议的实用性

### 第四阶段验证
- ✅ 最终判断的综合性
- ✅ 5轮分析格式的完整性
- ✅ 与原系统格式的兼容性
- ✅ 投票统计的合理性

## ⚠️ 注意事项

### 运行环境
- 必须使用 `tradingagents` 虚拟环境
- 确保网络连接正常（需要调用API）
- 确保有足够的磁盘空间保存结果

### 执行顺序
- 第三阶段依赖第二阶段的分类结果
- 第四阶段依赖前三个阶段的结果
- 建议按顺序执行或使用完整测试脚本

### 性能考虑
- 每个阶段之间有1秒延迟避免API限制
- 完整测试可能需要较长时间（取决于文件数量）
- 建议先用少量文件测试验证功能

### 错误处理
- 单个文件失败不会影响其他文件处理
- 详细错误信息会记录在日志文件中
- 可以重新运行失败的阶段

## 🎯 测试成功标准

### 功能性测试
- ✅ 所有四个阶段都能成功运行
- ✅ 输出格式符合预期规范
- ✅ 结果保存完整无误
- ✅ 统计信息准确

### 准确性测试
- ✅ 合理性判断符合预期
- ✅ 问题分类基本准确
- ✅ 合规评估逻辑正确
- ✅ 最终决策综合合理

### 兼容性测试
- ✅ 第四阶段输出与原系统格式兼容
- ✅ 文件保存机制与原系统一致
- ✅ 可以无缝替换原有单一模型调用

## 🚀 开始测试

现在您可以开始测试了：

```bash
# 激活环境
conda activate tradingagents

# 开始第一阶段测试
python test_stage1_complete.py
```

测试过程中会显示详细的进度信息和结果统计，请耐心等待完成。
